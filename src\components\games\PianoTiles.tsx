import React, { useState, useEffect, useCallback, useRef } from "react";

interface PianoTilesProps {
  onGameEnd?: (finalScore: number) => void;
}

const PianoTiles: React.FC<PianoTilesProps> = ({ onGameEnd }) => {
  const [score, setScore] = useState(0);
  const [gameOver, setGameOver] = useState(false);
  const [gameStarted, setGameStarted] = useState(false);
  const [tiles, setTiles] = useState<any[]>([]);
  const [speed, setSpeed] = useState(2);
  const gameAreaRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number | null>(null);
  const lastTileRef = useRef(0);
  const tileIdRef = useRef(0);

  const COLUMNS = 4;
  const TILE_HEIGHT = 120;
  const SPAWN_INTERVAL = 1000; // milliseconds

  // Generate a new tile
  const generateTile = useCallback(() => {
    const column = Math.floor(Math.random() * COLUMNS);
    const newTile = {
      id: tileIdRef.current++,
      column,
      position: -TILE_HEIGHT,
      clicked: false,
    };
    setTiles((prev) => [...prev, newTile]);
  }, []);

  // Start the game
  const startGame = () => {
    setGameStarted(true);
    setGameOver(false);
    setScore(0);
    setTiles([]);
    setSpeed(2);
    tileIdRef.current = 0;
    lastTileRef.current = Date.now();
    generateTile();
  };

  // Handle tile click
  const handleTileClick = useCallback(
    (tileId: number) => {
      setTiles((prev) =>
        prev.map((tile) =>
          tile.id === tileId ? { ...tile, clicked: true } : tile
        )
      );
      const newScore = score + 1;
      setScore(newScore);
      // Remove onScoreUpdate call - only call API on game end
    },
    [score]
  );

  // Game loop
  const gameLoop = useCallback(() => {
    if (!gameStarted || gameOver) return;

    const now = Date.now();

    // Generate new tiles
    if (now - lastTileRef.current > SPAWN_INTERVAL) {
      generateTile();
      lastTileRef.current = now;
    }

    // Update tile positions
    setTiles((prev) => {
      const updatedTiles = prev.map((tile) => ({
        ...tile,
        position: tile.position + speed,
      }));

      // Check for game over (tile reached bottom without being clicked)
      const gameOverTile = updatedTiles.find(
        (tile) => tile.position > 600 && !tile.clicked
      );

      if (gameOverTile) {
        setGameOver(true);
        setGameStarted(false);
        onGameEnd?.(score);
        return updatedTiles;
      }

      // Remove tiles that are off screen
      return updatedTiles.filter((tile) => tile.position < 700);
    });

    // Increase speed gradually
    setSpeed((prev) => Math.min(prev + 0.001, 5));

    animationRef.current = requestAnimationFrame(gameLoop);
  }, [gameStarted, gameOver, speed, generateTile, onGameEnd, score]);

  // Start game loop
  useEffect(() => {
    if (gameStarted && !gameOver) {
      animationRef.current = requestAnimationFrame(gameLoop);
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [gameStarted, gameOver, gameLoop]);

  // Restart game
  const restartGame = () => {
    startGame();
  };

  // Handle clicks/touches on game area (miss detection)
  const handleGameAreaInteraction = (
    clientX: number,
    clientY: number,
    currentTarget: Element
  ) => {
    if (!gameStarted || gameOver) return;

    const rect = currentTarget.getBoundingClientRect();
    const x = clientX - rect.left;
    const y = clientY - rect.top;

    // Check if click was on a tile
    const clickedOnTile = tiles.some((tile) => {
      const tileLeft = tile.column * (100 / COLUMNS);
      const tileRight = tileLeft + 100 / COLUMNS;
      const tileTop = tile.position;
      const tileBottom = tile.position + TILE_HEIGHT;

      const xPercent = (x / rect.width) * 100;

      return (
        !tile.clicked &&
        xPercent >= tileLeft &&
        xPercent <= tileRight &&
        y >= tileTop &&
        y <= tileBottom
      );
    });

    if (!clickedOnTile) {
      setGameOver(true);
      setGameStarted(false);
      onGameEnd?.(score);
    }
  };

  // Handle clicks on game area (miss detection)
  const handleGameAreaClick = (e: React.MouseEvent) => {
    handleGameAreaInteraction(e.clientX, e.clientY, e.currentTarget);
  };

  // Touch handling for mobile
  const handleTouch = (e: React.TouchEvent) => {
    e.preventDefault();
    if (e.touches[0]) {
      handleGameAreaInteraction(
        e.touches[0].clientX,
        e.touches[0].clientY,
        e.currentTarget
      );
    }
  };

  return (
    <div className="piano-tiles-game w-full h-full bg-black text-white flex flex-col items-center justify-center relative overflow-hidden">
      {/* Game Header */}
      <div className="absolute top-4 left-4 right-4 z-10 flex justify-between items-center">
        <div className="text-2xl font-bold text-accent-cyan">
          Score: {score}
        </div>
        <div className="text-lg text-secondary-text">
          Speed: {speed.toFixed(1)}x
        </div>
      </div>

      {/* Game Area */}
      <div
        ref={gameAreaRef}
        className="w-full h-full relative cursor-pointer touch-none"
        onClick={handleGameAreaClick}
        onTouchStart={handleTouch}
      >
        {/* Score at top center */}
        <div className="absolute top-12 left-1/2 transform -translate-x-1/2 z-20">
          <div className="text-white text-6xl font-bold drop-shadow-lg">
            {score}
          </div>
        </div>

        {/* Column dividers */}
        {[...Array(COLUMNS)].map((_, index) => (
          <div
            key={index}
            className="absolute top-0 bottom-0 border-r border-white border-opacity-20 last:border-r-0"
            style={{
              left: `${(index * 100) / COLUMNS}%`,
              width: `${100 / COLUMNS}%`,
            }}
          />
        ))}

        {/* Tiles */}
        {tiles.map((tile) => (
          <div
            key={tile.id}
            className={`absolute transition-colors duration-150 cursor-pointer ${
              tile.clicked
                ? "bg-gradient-to-b from-emerald-400 to-green-500"
                : "bg-gradient-to-b from-blue-500 to-blue-700 hover:from-blue-400 hover:to-blue-600"
            }`}
            style={{
              left: `${(tile.column * 100) / COLUMNS}%`,
              width: `${100 / COLUMNS}%`,
              top: `${tile.position}px`,
              height: `${TILE_HEIGHT}px`,
              transform: tile.clicked ? "scale(0.95)" : "scale(1)",
            }}
            onClick={(e) => {
              e.stopPropagation();
              if (!tile.clicked) {
                handleTileClick(tile.id);
              }
            }}
            onTouchStart={(e) => {
              e.stopPropagation();
              e.preventDefault();
              if (!tile.clicked) {
                handleTileClick(tile.id);
              }
            }}
          />
        ))}
      </div>

      {/* Instructions */}
      {!gameStarted && !gameOver && (
        <div className="absolute inset-0 bg-black/75 flex items-center justify-center text-white font-sans z-20">
          <div className="max-w-sm p-8 rounded-2xl text-center">
            <h1 className="text-2xl font-bold text-accent-cyan mb-4">
              Piano Tiles
            </h1>
            <p className="mb-3 text-sm">
              Tap the black tiles as they fall down
            </p>
            <p className="mb-3 text-sm">Don't let any tile reach the bottom!</p>
            <button
              onClick={startGame}
              className="px-6 py-3 bg-gradient-to-r from-accent-cyan to-xp-purple text-white font-bold rounded-lg hover:scale-105 transition-all duration-300"
            >
              Start Game
            </button>
          </div>
        </div>
      )}

      {/* Results */}
      {gameOver && (
        <div className="absolute inset-0 bg-black/75 flex items-center justify-center text-white font-sans z-20">
          <div className="max-w-sm p-8 rounded-2xl text-center">
            <h2 className="text-xl font-bold text-red-400 mb-3">Game Over!</h2>
            <p className="mb-2 text-sm">Final Score: {score}</p>
            <p className="mb-4 text-sm">
              {score > 50
                ? "Excellent performance!"
                : score > 25
                ? "Good job!"
                : "Keep practicing!"}
            </p>
            <button
              onClick={restartGame}
              className="px-6 py-3 bg-gradient-to-r from-accent-cyan to-xp-purple text-white font-bold rounded-lg hover:scale-105 transition-all duration-300"
            >
              Play Again
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default PianoTiles;
